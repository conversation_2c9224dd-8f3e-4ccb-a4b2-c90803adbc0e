


interface HeroProps {
  scrollY: number;
}

const Hero = ({ scrollY }: HeroProps) => {

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center relative hero-critical critical-content"
      style={{
        padding: `${scrollY * 0.1}px var(--space-4) var(--section-padding-lg)`,
        // Optimize for LCP - ensure content is visible immediately
        minHeight: '100vh',
        contain: 'layout style paint',
        // Increased top padding to create more space from navigation
        paddingTop: `calc(6rem + ${scrollY * 0.1}px)`,
      }}
    >

      <div
        className="text-center max-w-5xl mx-auto animate-fade-in"
        style={{
          transform: `translateY(${scrollY * 0.2}px)`,
        }}
      >
        <h1
          className="suz-section-title text-slate-100 mb-12 optimize-lcp"
          style={{
            // Optimize for LCP - ensure text is rendered immediately
            fontDisplay: 'swap',
            willChange: 'auto',
          }}
        >
          <span className="gradient-text-animated pulse-glow">Reinigung</span> neu definiert
        </h1>
        <p className="suz-text-heading-xl text-slate-300 mb-6 font-light">
          mit Stil, Präzision und Vertrauen
        </p>
        <p className="suz-text-body-xl text-slate-400 mb-16 max-w-3xl mx-auto" style={{ lineHeight: 'var(--line-height-relaxed)' }}>
          Zuverlässige Reinigung für Hotels, Büros und Wohnanlagen – von Profis für Profis.
        </p>

        <div className="flex flex-col sm:flex-row gap-6 justify-center">
          <a
            href="https://wa.me/4917623152477"
            target="_blank"
            rel="noopener noreferrer"
            className="suz-btn-primary premium-button-enhanced premium-button-3d bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 px-12 py-6 rounded-full text-lg font-semibold shadow-2xl btn-optimized optimize-fid group relative overflow-hidden"
            aria-label="Kontakt über WhatsApp aufnehmen"
          >
            <span className="relative z-10 group-hover:scale-105 transition-all duration-300 flex items-center gap-3">
              <svg className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.486"/>
              </svg>
              WhatsApp Kontakt
            </span>
          </a>
          <a
            href="mailto:<EMAIL>"
            className="suz-btn-primary premium-button-enhanced premium-button-3d bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-12 py-6 rounded-full text-lg font-semibold shadow-2xl btn-optimized optimize-fid group relative overflow-hidden"
            aria-label="E-Mail an SUZ Reinigung senden"
          >
            <span className="relative z-10 group-hover:scale-105 transition-all duration-300 flex items-center gap-3">
              <svg className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              E-Mail senden
            </span>
          </a>
        </div>

      </div>

      {/* Enhanced Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="floating-element absolute top-20 left-10 w-40 h-40 bg-blue-300/20 rounded-full blur-3xl"></div>
        <div className="floating-element absolute top-40 right-20 w-32 h-32 bg-cyan-300/20 rounded-full blur-2xl" style={{animationDelay: '2s'}}></div>
        <div className="floating-element absolute bottom-20 left-1/4 w-48 h-48 bg-sky-300/20 rounded-full blur-3xl" style={{animationDelay: '4s'}}></div>
      </div>
    </section>
  );
};

export default Hero;
